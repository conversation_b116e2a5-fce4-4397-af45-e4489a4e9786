<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Project Details</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/project-statistics.css">
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
        <script type="module" src="/src/utils/theme.js"></script>
    </header>
    <div>
        <div class="btn-list">
            <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
            <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
            <button class="thistime" onclick="window.location.href='../project/statistics.html';">數據統計</button>
            <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
            <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
            <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
        </div>
        <p class="projectName">［專案名稱］</p>
        <div class="project-statistics-maincontent">
            <h2 class="title-name">數據統計</h2>
            <nav>
                <input type="button" id="export_data_btn" value="export"></input>
                <table id="statistics-table">
                    <thead>
                        <tr id="titles">
                            <th data-i18n="exhibition_name">ExhibitionName</th>
                            <th data-i18n="name">Name</th>
                            <th data-i18n="impressions">Impressions</th>
                            <th data-i18n="time_spent">Time Spent</th>
                            <th data-i18n="duration">Duration</th>
                        </tr>
                    </thead>
                    <tbody id="content"></tbody>
                </table>
                <script type="module" src="../../src/project/title.js"></script>
                <script src="../../src/project/statistics.js" type="module"></script>
        </div>
    </div>
</body>

</html>