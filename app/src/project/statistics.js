import * as tokenOps from '../user/tokenOps.js';
import * as tableOps from '../table.js';
import { exportData } from '../export.js';
import {translationText} from "../translation.js";

// Check if user is authenticated
var token = tokenOps.getToken();
if (!token || !tokenOps.validateToken()) {
    console.log('No valid token found, redirecting to login');
    window.location.href = '../../index.html';
}

var data = JSON.parse(localStorage.getItem("project_data"));
if (!data || !data[0]) {
    console.log('No project data found, using test data');
    // Set test data for development
    data = [1, "測試專案"];
    localStorage.setItem("project_data", JSON.stringify(data));
}

var exportDataBtn = document.getElementById('export_data_btn');
var titles = document.getElementById("titles");
var where = encodeURIComponent(JSON.stringify({ pid:data[0] }));
var titleTranslationTexts;

document.onload=async()=>{
    exportDataBtn.value = await translationText("export");
    titleTranslationTexts = [await translationText("exhibition_name"), await translationText("name"), await translationText("impressions"), await translationText("time_spent"), await translationText("duration")];
    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
}

var table = tableOps.initTable('#statistics-table');
tableOps.updateTable(table,['videos','videoPlayStats'],['ExhibitionName','name','impressions', "TimeSpent","duration"], `pid=${data[0]}`);
setInterval(()=>{
    tableOps.updateTable(table,['videos','videoPlayStats'],['ExhibitionName','name','impressions', "TimeSpent","duration"], `pid=${data[0]}`);
},3000);

exportDataBtn.addEventListener('click',function(){
    if (data && data[0]) {
        exportData('videos/videoPlayStats', `pid=${data[0]}`);
    } else {
        alert('No project data available for export');
    }
});

