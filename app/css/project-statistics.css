/* Project Statistics Page Styles */
label {
	color: var(--text-accent);
}

body {
	color: var(--text-primary);
}

.title-name {
	color: var(--text-accent);
}

.top-title {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-bottom: 1rem;
}

.list-title,
.memberlist-title {
	color: var(--text-primary);
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 1rem;
}

.projectName {
	position: relative;
	width: min(90vw, 400px);
	left: 50%;
	text-align: center;
	font-size: 2rem;
	font-weight: 600;
	color: var(--text-accent);
	transform: translate(-50%, 0);
	margin: 2rem 0;
	padding: 1rem;
	background-color: var(--bg-secondary);
	border-radius: 0.5rem;
	border: 1px solid var(--border-color);
}

.project-statistics-maincontent {
	position: relative;
	padding: 2rem;
	width: min(90vw, 1200px);
	min-height: calc(100vh - 200px);
	left: 50%;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	overflow-y: auto;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}

#statistics-table {
	width: 100%;
}

::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: var(--bg-tertiary);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--text-accent);
}

.groupContent {
	display: flex;
	gap: 2rem;
	flex-wrap: wrap;
}

.group-list,
.member-list {
	width: 100%;
	flex: 1;
	min-width: 300px;
	background-color: var(--bg-tertiary);
	border-radius: 1rem;
	padding: 2rem;
	border: 1px solid var(--border-color);
	box-shadow: 0 2px 8px var(--shadow);
}

/* Action Buttons */
#create_btn,
#delete_btn,
#leave_btn {
	background-color: transparent;
	font-size: 1.125rem;
	color: var(--text-accent);
	border: 1px solid var(--text-accent);
	border-radius: 0.375rem;
	padding: 0.5rem 1rem;
	cursor: pointer;
	transition: all 0.2s ease;
	font-weight: 500;
}

#create_btn:hover,
#delete_btn:hover,
#leave_btn:hover {
	background-color: var(--text-accent);
	color: var(--bg-primary);
}

/* Table Styles */
.table {
	width: 100%;
	border-collapse: collapse;
	background-color: var(--bg-secondary);
	border-radius: 0.5rem;
	overflow: hidden;
	box-shadow: 0 2px 8px var(--shadow);
}

.table th,
.table td {
	padding: 1rem;
	text-align: left;
	border-bottom: 1px solid var(--border-color);
	color: var(--text-primary);
}

.table th {
	background-color: var(--bg-tertiary);
	font-weight: 600;
	color: var(--text-accent);
}

/* Side Navigation */
.btn-list {
	position: fixed;
	bottom: 3rem;
	display: flex;
	left: 5vw;
	flex-direction: column;
	gap: 1.25rem;
	align-items: flex-start;
}

.btn-list button {
	color: var(--text-accent);
	width: 50vw;
	height: 2.5rem;
	font-size: 1.125rem;
	text-align: left;
	border: none;
	box-shadow: none;
	background-color: transparent;
	outline: none;
	cursor: pointer;
	transition: all 0.2s ease;
	padding: 0.5rem;
	border-radius: 0.375rem;
}

.btn-list button:hover {
	background-color: var(--hover-bg);
	color: var(--text-primary);
}

.thistime {
	background-color: var(--bg-secondary) !important;
	border: 1px solid var(--text-accent);
	color: var(--text-accent) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
	.project-statistics-maincontent {
		width: 95vw;
		padding: 1.5rem;
		margin-top: 1rem;
	}

	.groupContent {
		flex-direction: column;
		gap: 1.5rem;
	}

	.group-list,
	.member-list {
		min-width: auto;
	}

	.btn-list {
		left: 2vw;
	}

	.btn-list button {
		width: 60vw;
		font-size: 1rem;
	}
}

@media (max-width: 480px) {
	.project-statistics-maincontent {
		width: 98vw;
		padding: 1rem;
	}

	.projectName {
		font-size: 1.5rem;
		width: 95vw;
	}

	.table th,
	.table td {
		padding: 0.5rem;
		font-size: 0.875rem;
	}
}