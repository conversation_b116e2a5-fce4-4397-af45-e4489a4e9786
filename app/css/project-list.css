/* Project List Specific Styles */
label {
	color: var(--text-primary);
}

.title {
	color: var(--text-accent);
}

.list-title {
	color: var(--text-accent);
	font-size: 1.25rem;
	font-weight: bold;
}

.top-title {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-bottom: 1rem;
}

.project-list-place {
	position: relative;
	padding: 2rem;
	width: min(90vw, 1200px);
	left: 50vw;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}

#projects-table {
	width: 100%;
	color: var(--text-primary);
	padding-top: 1rem;
}

#create_project_btn {
	background-color: transparent;
	width: 24px;
	height: 24px;
	color: var(--text-accent);
	border: none;
	box-shadow: none;
	outline: none;
	background-image: url(../html/assets/add_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
	cursor: pointer;
	transition: all 0.2s ease;
	border-radius: 0.25rem;
	padding: 0.25rem;
}

#create_project_btn:hover {
	background-color: var(--hover-bg);
	transform: scale(1.1);
}

#delete_project_btn {
	background-color: transparent;
	width: 24px;
	height: 24px;
	color: var(--text-accent);
	border: none;
	box-shadow: none;
	outline: none;
	background-image: url(../html/assets/bin_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
	cursor: pointer;
	transition: all 0.2s ease;
	border-radius: 0.25rem;
	padding: 0.25rem;
}

#delete_project_btn:hover {
	background-color: var(--hover-bg);
	transform: scale(1.1);
}

#join_group_btn {
	background-color: transparent;
	font-size: 1.125rem;
	color: var(--text-accent);
	border: 1px solid var(--text-accent);
	box-shadow: none;
	outline: none;
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	cursor: pointer;
	transition: all 0.2s ease;
	font-weight: 500;
}

#join_group_btn:hover {
	background-color: var(--text-accent);
	color: var(--bg-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
	.header-content {
		padding: 1rem;
		flex-direction: column;
		gap: 1rem;
	}

	.header-nav {
		flex-wrap: wrap;
		justify-content: center;
		gap: 0.5rem;
	}

	.project-list-place {
		width: 95vw;
		padding: 1rem;
		margin-top: 1rem;
	}

	.top-title {
		flex-direction: column;
		align-items: flex-start;
		gap: 1rem;
	}

	#projects-table {
		font-size: 0.875rem;
	}

	th,
	td {
		padding: 0.5rem;
	}

	.title {
		font-size: 1.5rem;
	}

	.nav-link {
		padding: 0.375rem 0.75rem;
		font-size: 0.875rem;
	}

	.dropdown-menu {
		position: fixed;
		top: auto;
		bottom: 0;
		left: 0;
		right: 0;
		border-radius: 1rem 1rem 0 0;
		min-width: auto;
	}
}

@media (max-width: 480px) {
	.project-list-place {
		width: 98vw;
		padding: 0.75rem;
	}

	.title {
		font-size: 1.25rem;
	}

	.list-title {
		font-size: 1rem;
	}

	#projects-table {
		font-size: 0.75rem;
	}

	th,
	td {
		padding: 0.375rem;
	}

	.header-nav {
		gap: 0.25rem;
	}

	.nav-link {
		padding: 0.25rem 0.5rem;
		font-size: 0.75rem;
	}
}