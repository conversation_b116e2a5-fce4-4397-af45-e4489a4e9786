/* CSS Variables for Theme Colors */
:root {
    /* Dark theme (default) */
    --bg-primary: #000000;
    --bg-secondary: #202020;
    --bg-tertiary: #333333;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-accent: #6F8FF6;
    --border-color: #444444;
    --hover-bg: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
    --input-bg: #444444;
    --button-primary: #4CAF50;
    --button-primary-hover: #45a049;
}

/* Light theme */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-accent: #0d6efd;
    --border-color: #dee2e6;
    --hover-bg: #e9ecef;
    --shadow: rgba(0, 0, 0, 0.1);
    --input-bg: #ffffff;
    --button-primary: #198754;
    --button-primary-hover: #157347;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    position: relative;
    margin: 0;
    padding: 0;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header Styles */
header {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0;
    box-shadow: 0 2px 4px var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.title {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-accent);
    margin: 0;
}

.header-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.theme-toggle:hover {
    background-color: var(--hover-bg);
    transform: scale(1.1);
}

.theme-icon {
    font-size: 1.2rem;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    display: block;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--hover-bg);
    color: var(--text-accent);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px var(--shadow);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: block;
    transition: background-color 0.2s ease;
    border-radius: 0.375rem;
    margin: 0.25rem;
}

.dropdown-item:hover {
    background-color: var(--hover-bg);
    color: var(--text-accent);
}

/* Input Styles */
input {
    background-color: var(--input-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

/* List Styles */
.list-container {
    display: flex;
}

ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

li {
    justify-self: center;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-secondary);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow);
}

thead {
    background-color: var(--bg-tertiary);
    width: 100%;
}

th,
td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 600;
    color: var(--text-accent);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

th:hover {
    background-color: var(--hover-bg);
}

tr:hover {
    background-color: var(--hover-bg);
}

th.resizable {
    position: relative;
}

th.resizable div {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    cursor: col-resize;
}

.selected {
    background-color: var(--hover-bg);
}

/* Form Elements */
select {
    font-size: 1rem;
    background-color: var(--input-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.5rem;
}

#content-section {
    height: 70vh;
    padding: 1.25rem;
}

#action-section {
    position: fixed;
    bottom: 1.25rem;
    right: 50%;
    transform: translate(50%, 0);
    margin-top: 75px;
    padding: 0.625rem;
    border: 2px solid var(--border-color);
    background-color: var(--bg-secondary);
    border-radius: 0.5rem;
    z-index: 1;
}

#action-section button {
    background-color: var(--button-primary);
    color: white;
    padding: 0.625rem 0.9375rem;
    border: none;
    border-radius: 0.3125rem;
    cursor: pointer;
    margin-bottom: 0.3125rem;
    transition: background-color 0.2s ease;
}

#action-section button:hover {
    background-color: var(--button-primary-hover);
}

/* Utility Classes */
.no-indent {
    list-style-type: none;
    padding-left: 0;
    margin-left: 0;
}

/* Form Styles */
form {
    font-size: 1rem;
    margin: 1.25rem;
    max-width: 1200px;
}

form label {
    display: block;
    margin-top: 0.625rem;
    font-weight: 600;
    color: var(--text-primary);
}

input[type="text"],
input[type="number"],
input[type="time"],
input[type="password"],
textarea {
    width: 100%;
    color: var(--text-primary);
    padding: 0.75rem;
    margin-top: 0.3125rem;
    margin-bottom: 0.9375rem;
    font-size: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--input-bg);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus,
textarea:focus {
    border-color: var(--text-accent);
    outline: none;
    box-shadow: 0 0 0 3px rgba(111, 143, 246, 0.1);
}

button[type="submit"] {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    background-color: var(--button-primary);
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-weight: 500;
}

button[type="submit"]:hover {
    background-color: var(--button-primary-hover);
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.left {
    float: left;
    width: 49%;
}

.right {
    float: right;
    width: 49%;
}

@media (max-width: 800px) {

    input[type="number"],
    input[type="time"] {
        max-width: 100%;
    }

    .left {
        float: none;
        width: 100%;
    }

    .right {
        float: none;
        width: 100%;
    }
}

/* Popup container */
.popup {
    display: none;
}

.popup.show {
    position: absolute;
    display: block;
    min-width: 160px;
    max-width: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.details-header {
    box-shadow: 0 1px 0 #ddd;
    margin-bottom: 10px;
}

#languageBtn {
    color: white;
    font-size: 20px;
    padding-top: 10px;
}

#openUserDialog {
    color: white;
    font-size: 20px;
    padding-top: 10px;
}

#projectsBtn {
    color: white;
    font-size: 20px;
    padding-top: 10px;
}