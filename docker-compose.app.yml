services:
  app:
    image: node:24-alpine
    container_name: iamm-app
    ports:
      - "3061:3061"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/backend/node_modules
    working_dir: /app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://root:password@mysql:3306/iamm
      - JWT_SECRET=IAMMSecretPassword
    command: sh -c "npm install && npm run docker:dev"

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3061/health"]
      timeout: 10s
      retries: 5
      interval: 30s

  backend:
    image: node:24-alpine
    container_name: iamm-backend
    ports:
      - "3062:3062"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/backend/node_modules
    working_dir: /app/backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://root:password@mysql:3306/iamm
      - JWT_SECRET=IAMMSecretPassword
    command: sh -c "npm install && npm run docker:dev"

    restart: unless-stopped


networks:
  default:
    name: iamm_iamm
    external: true
