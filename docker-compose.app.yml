services:
  app:
    image: node:24-alpine
    container_name: iamm-app
    ports:
      - "5173:5173"
    volumes:
      - ./app:/app/app
      - ./package.json:/app/package.json
      - /app/node_modules
    working_dir: /app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://root:password@mysql:3306/iamm
      - JWT_SECRET=IAMMSecretPassword
    command: sh -c "npm install && npm run frontend:dev"

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173"]
      timeout: 10s
      retries: 5
      interval: 30s

  backend:
    image: node:24-alpine
    container_name: iamm-backend
    ports:
      - "3062:3062"
    volumes:
      - .:/app
      - /app/backend/node_modules
    working_dir: /app/backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://root:password@mysql:3306/iamm
      - JWT_SECRET=IAMMSecretPassword
    command: sh -c "npm install && npx prisma generate --schema=../prisma/schema.prisma && npm run dev"

    restart: unless-stopped


networks:
  default:
    name: iamm_iamm
    external: true
