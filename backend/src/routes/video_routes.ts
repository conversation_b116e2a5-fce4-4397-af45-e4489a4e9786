import express from 'express';
import {auth} from "../auth";
import multer from "multer";
import path from "path";
import fs from 'fs';
import * as s3 from '../utils/s3';
import * as uuid from 'uuid';
const { Readable } = require('stream');

const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, "/home/<USER>/Videos"); // 指定儲存路徑
    },
    filename: (req, file, cb) => {
        const id = uuid.v4();
        cb(null, id + path.extname(file.originalname)); // 使用當前時間作為檔案名稱
    }
});

const upload = {
    "local":multer({ storage: storage }),
    "s3":multer({ storage: multer.memoryStorage()})
};

const bufferToStream = (buffer:Buffer) => {
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // 標示流結束
    return stream;
};

export const router = express.Router({ mergeParams: true });

router.post('/api/v1/local/video', upload['local'].single('video'), (req, res) => {
    if (!req.file) {
        res.status(400).send('No file uploaded.');
    }
    res.json({ url: req.file?.filename});
});

router.get('/api/v1/local/video/:filename', (req, res) => {
    const filePath = `/home/<USER>/Videos/${req.params.filename}`;
    fs.stat(filePath, (err, stats) => {
        if (err || !stats.isFile()) {
            return res.status(404).send('File not found');
        }
        res.type('video/mp4');
        const stream = fs.createReadStream(filePath);
        stream.pipe(res);
    });
    // TypeScript 需要明確的返回，即使這是異步操作
    return;
});

router.delete('/api/v1/local/video', async (req,res) => {
    const filePath = `/home/<USER>/Videos/${req.body.filename}`;
    try{
        fs.unlinkSync(filePath);
        res.json({ message: 'File deleted.'});
    }catch(e){
        res.status(400).json({error: e})
    }
});

router.post('/api/v1/s3/video', upload['s3'].single('video'), async (req,res) => {
    let file = req.file;
    const id = uuid.v4();
    const extName = req.file?.originalname.match(/\.([^.]+)$/)![1];
    try{
        await s3.addObject(`${id}.${extName}`, file?.buffer!);
        res.status(200).json({url: `${id}.${extName}`})
      }
      catch(e){
        console.error(e);
      }
});

router.get('/api/v1/s3/video/:filename', async (req, res) => {
  let filename = req.params.filename;  
  const file = await s3.getObject(filename);
  res.type('video/mp4');
  const stream = bufferToStream(file as Buffer);
  stream.pipe(res); 
})

router.delete('/api/v1/s3/video', async (req,res) => {
    const filePath = req.body.filename;
    try{
        await s3.deleteObject(filePath);
        res.json({ message: 'File deleted.'});
    }catch(e){
        res.status(400).json({error: e})
    }
});

// router.get("/api/v1/s3/video/list", async (req, res)=>{
//     try{
//         let contents = await s3.listObjects();
//         res.json({ message: contents});
//     }catch(e){
//         res.status(400).json({error: e})
//     }
// })