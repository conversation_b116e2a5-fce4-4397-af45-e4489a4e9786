# IAMM Docker 部署指南

## 🚀 快速啟動

### 一鍵啟動（推薦）
```bash
npm run docker:start
```

### 手動啟動
```bash
# 1. 啟動 MySQL
npm run docker:mysql

# 2. 等待 30 秒讓 MySQL 完全啟動
sleep 30

# 3. 初始化資料庫
npm run docker:init-db

# 4. 啟動應用程式
npm run docker:app
```

## 🛑 停止服務

```bash
npm run docker:stop
```

## 📊 監控系統

```bash
# 啟動 Grafana + Prometheus 監控
npm run docker:monitoring

# 訪問 Grafana: http://localhost:3000
# 帳號: admin / admin123
```

## 🔧 常用指令

### Docker 管理
```bash
# 查看運行狀態
docker ps

# 查看應用程式日誌
npm run docker:logs

# 查看 MySQL 日誌
npm run docker:logs-mysql

# 重啟服務
npm run docker:restart

# 完全清理（包含數據卷）
npm run docker:clean
```

### 資料庫管理
```bash
# 生成 Prisma 客戶端
npm run prisma:generate

# 執行資料庫遷移
npm run prisma:migrate

# 重置資料庫
npm run prisma:reset

# 打開 Prisma Studio
npm run prisma:studio
```

### PM2 管理（本地開發）
```bash
# 啟動所有 PM2 服務
npm run pm2:start

# 停止所有 PM2 服務
npm run pm2:stop

# 查看 PM2 狀態
npm run pm2:status

# 查看 PM2 日誌
npm run pm2:logs
```

## 🌐 訪問地址

| 服務 | 地址 | 說明 |
|------|------|------|
| 前端應用 | http://localhost:5173 | Vite 開發服務器 |
| 後端 API | http://localhost:3061 | Express API 服務器 |
| 健康檢查 | http://localhost:3061/health | API 健康狀態 |
| Grafana | http://localhost:3000 | 監控儀表板 |
| Prometheus | http://localhost:9090 | 指標收集 |
| Prisma Studio | http://localhost:5555 | 資料庫管理 |

## 📁 專案結構

```
iamm/
├── app/                    # 前端應用
│   ├── src/               # 源代碼
│   ├── html/              # HTML 頁面
│   ├── package.json       # 前端依賴
│   └── Dockerfile         # 前端 Docker 配置
├── backend/               # 後端 API
│   ├── src/               # 源代碼
│   ├── package.json       # 後端依賴
│   └── Dockerfile         # 後端 Docker 配置
├── prisma/                # 資料庫 Schema
├── mysql/                 # MySQL 初始化腳本
├── monitoring/            # Grafana/Prometheus 配置
├── scripts/               # 啟動腳本

├── docker-compose.mysql.yml    # MySQL 服務
├── docker-compose.app.yml      # 應用服務
└── docker-compose.monitoring.yml # 監控服務
```

## 🔧 配置說明

### 資料庫配置
- **主機**: localhost:3306 (本地) / mysql:3306 (Docker)
- **資料庫**: iamm
- **用戶**: root
- **密碼**: password

### 環境變數
```bash
# 本地開發
DATABASE_URL=mysql://root:password@localhost:3306/iamm

# Docker 環境
DATABASE_URL=mysql://root:password@mysql:3306/iamm
JWT_SECRET=IAMMSecretPassword
```

## 🐛 故障排除

### MySQL 連接問題
```bash
# 檢查 MySQL 容器狀態
docker ps | grep mysql

# 查看 MySQL 日誌
docker logs iamm-mysql

# 測試連接
docker exec -it iamm-mysql mysql -u root -ppassword -e "SHOW DATABASES;"
```

### 應用程式啟動問題
```bash
# 查看後端日誌
docker logs iamm-backend

# 查看前端日誌
docker logs iamm-frontend

# 檢查健康狀態
curl http://localhost:3061/health
```

### 端口衝突
```bash
# 檢查端口使用情況
lsof -i :3306  # MySQL
lsof -i :3061  # 後端
lsof -i :5173  # 前端

# 停止佔用端口的進程
npm run kill:all
```

### 資料庫遷移問題
```bash
# 重置資料庫
npm run prisma:reset

# 手動執行遷移
npm run prisma:migrate

# 檢查資料庫狀態
npm run prisma:studio
```

## 📝 開發流程

### 1. 初次設置
```bash
# 安裝依賴
npm run setup

# 啟動 Docker 服務
npm run docker:start

# 訪問應用
open http://localhost:5173
```

### 2. 日常開發
```bash
# 啟動開發環境
npm run docker:start

# 修改代碼（熱重載）
# 前端: app/src/
# 後端: backend/src/

# 查看日誌
npm run docker:logs
```

### 3. 資料庫變更
```bash
# 修改 Schema
vim prisma/schema.prisma

# 生成遷移
npm run prisma:migrate

# 重啟後端服務
docker restart iamm-backend
```

## 🚀 生產部署

### 1. 建置應用
```bash
npm run build
```

### 2. 使用 PM2 部署
```bash
npm run pm2:start
```

### 3. 使用 Docker 部署
```bash
# 設置生產環境變數
export NODE_ENV=production
export DATABASE_URL=mysql://user:pass@prod-host:3306/iamm

# 啟動生產服務
docker-compose up -d
```

## 📞 支援

如遇問題，請檢查：
1. Docker 是否正常運行
2. 端口是否被佔用
3. 環境變數是否正確設置
4. 資料庫連接是否正常

更多詳細信息請參考 `DEPLOYMENT_GUIDE.md`。
